#include "../CSV/CSVReader.h"
#include "../Pojo/PitchData.h"
#include <QApplication>
#include <QDebug>
#include <QtTest/QtTest>

class SimpleTest : public QObject {
    Q_OBJECT

  private slots:
    void testBasicCSVReading();
};

void SimpleTest::testBasicCSVReading() {
    qDebug() << "开始基本CSV读取测试";

    // 创建一个简单的测试CSV文件
    QString testFile = "simple_test.csv";
    QFile   file(testFile);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");  // 设置UTF-8编码
        // 根据CSVReader的实现，格式应该是：电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限
        out << "12345678,MCU001234,V1.6.15.11.25.2.25,1,1.297428,2.583963,0.000000,5.000000\n";
        file.close();

        qDebug() << "测试文件创建成功:" << testFile;

        // 尝试读取
        CSVReader          reader;
        QVector<PitchData> data = reader.readDataFromCsv(testFile);

        qDebug() << "读取到数据条数:" << data.size();

        if (data.size() > 0) {
            qDebug() << "第一条数据电机标签:" << data[0].getNbr();
            QVERIFY(data.size() == 1);
            QCOMPARE(data[0].getNbr(), QString("12345678"));
        } else {
            qDebug() << "未读取到数据";
            QFAIL("未能读取到测试数据");
        }

        // 清理测试文件
        QFile::remove(testFile);
    } else {
        QFAIL("无法创建测试文件");
    }

    qDebug() << "基本CSV读取测试完成";
}

QTEST_MAIN(SimpleTest)
#include "SimpleTest.moc"
