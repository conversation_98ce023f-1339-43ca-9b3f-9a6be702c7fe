cmake_minimum_required(VERSION 3.16)
project(MESTests)

# 设置C++标准（与主程序保持一致）
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置源文件编码为UTF-8（与主程序保持一致）
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -finput-charset=UTF-8")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -finput-charset=UTF-8")

# Windows平台额外设置（Visual Studio）
if(MSVC)
    # 设置编码
    add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
    add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
    add_compile_options(/utf-8) # Visual Studio 2015及以上支持
endif()

# 查找Qt（与主程序保持一致）
find_package(QT NAMES Qt6 Qt5 COMPONENTS Core Widgets Sql Test REQUIRED)
find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Core Widgets Sql Test REQUIRED)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${CMAKE_CURRENT_BINARY_DIR})

# 公共源文件（主程序的核心组件）
set(COMMON_SOURCES
    ../CSV/CSVReader.cpp
    ../CSV/CSVWriter.cpp
    ../Pojo/PitchData.cpp
    ../Pojo/MESData.cpp
    ../Config/ConfigLoader.cpp
    ../Config/XmlConfig.cpp
    ../DataStore/DataStore.cpp
)

# 公共UI文件
set(COMMON_UI_FILES
    ../widget.ui
    ../Comm.ui
    ../MainForm.ui
)

# 处理UI文件
if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_wrap_ui(COMMON_UI_HEADERS ${COMMON_UI_FILES})
else()
    qt5_wrap_ui(COMMON_UI_HEADERS ${COMMON_UI_FILES})
endif()

# ===== 原有的MES功能测试 =====
set(MES_FUNCTION_TEST_SOURCES
    MESFunctionTest.cpp
    ../widget.cpp
    ${COMMON_SOURCES}
)

add_executable(MESFunctionTest ${MES_FUNCTION_TEST_SOURCES} ${COMMON_UI_HEADERS})

target_link_libraries(MESFunctionTest
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Sql
    Qt${QT_VERSION_MAJOR}::Test
)

set_target_properties(MESFunctionTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin
    AUTOMOC ON
    AUTOUIC ON
)

# ===== 新的MES模拟测试 =====
set(MES_SIMULATION_TEST_SOURCES
    MESSimulationTest.cpp
    TestDataGenerator.cpp
    MockMESUploader.cpp
    ../widget.cpp
    ${COMMON_SOURCES}
)

add_executable(MESSimulationTest ${MES_SIMULATION_TEST_SOURCES} ${COMMON_UI_HEADERS})

target_link_libraries(MESSimulationTest
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Sql
    Qt${QT_VERSION_MAJOR}::Test
)

set_target_properties(MESSimulationTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin
    AUTOMOC ON
    AUTOUIC ON
)

# 编译选项
if(WIN32)
    target_compile_definitions(MESFunctionTest PRIVATE WIN32_LEAN_AND_MEAN)
    target_compile_definitions(MESSimulationTest PRIVATE WIN32_LEAN_AND_MEAN)
endif()

# 添加调试定义（与主程序保持一致）
target_compile_definitions(MESFunctionTest PRIVATE QT_MESSAGELOGCONTEXT)
target_compile_definitions(MESSimulationTest PRIVATE QT_MESSAGELOGCONTEXT)
