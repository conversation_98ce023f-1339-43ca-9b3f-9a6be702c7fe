cmake_minimum_required(VERSION 3.16)
project(MESFunctionTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Sql Test)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)

# MES功能测试（调用实际代码）
set(MES_FUNCTION_TEST_SOURCES
    MESFunctionTest.cpp
    ../widget.cpp
    ../CSV/CSVReader.cpp
    ../CSV/CSVWriter.cpp
    ../Pojo/PitchData.cpp
    ../Pojo/MESData.cpp
    ../Config/ConfigLoader.cpp
    ../DataStore/DataStore.cpp
)

# 添加UI文件
set(UI_FILES
    ../widget.ui
)

# 处理UI文件
qt5_wrap_ui(UI_HEADERS ${UI_FILES})

# 创建可执行文件
add_executable(${PROJECT_NAME} ${MES_FUNCTION_TEST_SOURCES} ${UI_HEADERS})

# 链接Qt库
target_link_libraries(${PROJECT_NAME}
    Qt5::Core
    Qt5::Widgets
    Qt5::Sql
    Qt5::Test
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin
)

# 编译选项
if(WIN32)
    target_compile_definitions(${PROJECT_NAME} PRIVATE WIN32_LEAN_AND_MEAN)
endif()

# 启用Qt的自动MOC和UIC
set_target_properties(${PROJECT_NAME} PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
)
