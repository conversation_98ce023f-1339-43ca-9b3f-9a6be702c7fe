/****************************************************************************
** Meta object code from reading C++ file 'MESSimulationTest.cpp'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MESSimulationTest.cpp' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MESSimulationTest_t {
    QByteArrayData data[20];
    char stringdata0[457];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MESSimulationTest_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MESSimulationTest_t qt_meta_stringdata_MESSimulationTest = {
    {
QT_MOC_LITERAL(0, 0, 17), // "MESSimulationTest"
QT_MOC_LITERAL(1, 18, 12), // "initTestCase"
QT_MOC_LITERAL(2, 31, 0), // ""
QT_MOC_LITERAL(3, 32, 15), // "cleanupTestCase"
QT_MOC_LITERAL(4, 48, 4), // "init"
QT_MOC_LITERAL(5, 53, 7), // "cleanup"
QT_MOC_LITERAL(6, 61, 29), // "testNormalMESUploadSimulation"
QT_MOC_LITERAL(7, 91, 25), // "testDuplicateDataHandling"
QT_MOC_LITERAL(8, 117, 25), // "testWorkingCopyManagement"
QT_MOC_LITERAL(9, 143, 28), // "testStatusTrackingSimulation"
QT_MOC_LITERAL(10, 172, 30), // "testSignalEmissionVerification"
QT_MOC_LITERAL(11, 203, 33), // "testNetworkInterruptionSimula..."
QT_MOC_LITERAL(12, 237, 29), // "testDatabaseConnectionFailure"
QT_MOC_LITERAL(13, 267, 23), // "testInvalidDataHandling"
QT_MOC_LITERAL(14, 291, 26), // "testLargeDataSetProcessing"
QT_MOC_LITERAL(15, 318, 21), // "testUploadPerformance"
QT_MOC_LITERAL(16, 340, 30), // "testConcurrentUploadSimulation"
QT_MOC_LITERAL(17, 371, 25), // "testMemoryUsageMonitoring"
QT_MOC_LITERAL(18, 397, 30), // "testCompleteWorkflowSimulation"
QT_MOC_LITERAL(19, 428, 28) // "testResumeUploadAfterFailure"

    },
    "MESSimulationTest\0initTestCase\0\0"
    "cleanupTestCase\0init\0cleanup\0"
    "testNormalMESUploadSimulation\0"
    "testDuplicateDataHandling\0"
    "testWorkingCopyManagement\0"
    "testStatusTrackingSimulation\0"
    "testSignalEmissionVerification\0"
    "testNetworkInterruptionSimulation\0"
    "testDatabaseConnectionFailure\0"
    "testInvalidDataHandling\0"
    "testLargeDataSetProcessing\0"
    "testUploadPerformance\0"
    "testConcurrentUploadSimulation\0"
    "testMemoryUsageMonitoring\0"
    "testCompleteWorkflowSimulation\0"
    "testResumeUploadAfterFailure"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MESSimulationTest[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      18,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  104,    2, 0x08 /* Private */,
       3,    0,  105,    2, 0x08 /* Private */,
       4,    0,  106,    2, 0x08 /* Private */,
       5,    0,  107,    2, 0x08 /* Private */,
       6,    0,  108,    2, 0x08 /* Private */,
       7,    0,  109,    2, 0x08 /* Private */,
       8,    0,  110,    2, 0x08 /* Private */,
       9,    0,  111,    2, 0x08 /* Private */,
      10,    0,  112,    2, 0x08 /* Private */,
      11,    0,  113,    2, 0x08 /* Private */,
      12,    0,  114,    2, 0x08 /* Private */,
      13,    0,  115,    2, 0x08 /* Private */,
      14,    0,  116,    2, 0x08 /* Private */,
      15,    0,  117,    2, 0x08 /* Private */,
      16,    0,  118,    2, 0x08 /* Private */,
      17,    0,  119,    2, 0x08 /* Private */,
      18,    0,  120,    2, 0x08 /* Private */,
      19,    0,  121,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MESSimulationTest::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MESSimulationTest *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->initTestCase(); break;
        case 1: _t->cleanupTestCase(); break;
        case 2: _t->init(); break;
        case 3: _t->cleanup(); break;
        case 4: _t->testNormalMESUploadSimulation(); break;
        case 5: _t->testDuplicateDataHandling(); break;
        case 6: _t->testWorkingCopyManagement(); break;
        case 7: _t->testStatusTrackingSimulation(); break;
        case 8: _t->testSignalEmissionVerification(); break;
        case 9: _t->testNetworkInterruptionSimulation(); break;
        case 10: _t->testDatabaseConnectionFailure(); break;
        case 11: _t->testInvalidDataHandling(); break;
        case 12: _t->testLargeDataSetProcessing(); break;
        case 13: _t->testUploadPerformance(); break;
        case 14: _t->testConcurrentUploadSimulation(); break;
        case 15: _t->testMemoryUsageMonitoring(); break;
        case 16: _t->testCompleteWorkflowSimulation(); break;
        case 17: _t->testResumeUploadAfterFailure(); break;
        default: ;
        }
    }
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject MESSimulationTest::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_MESSimulationTest.data,
    qt_meta_data_MESSimulationTest,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MESSimulationTest::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MESSimulationTest::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MESSimulationTest.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int MESSimulationTest::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 18;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
