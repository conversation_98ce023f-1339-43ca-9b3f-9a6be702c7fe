#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QEventLoop>
#include <QFileInfo>
#include <QSignalSpy>
#include <QTimer>
#include <QtTest/QtTest>

#include "../CSV/CSVReader.h"
#include "../CSV/CSVWriter.h"
#include "../Config/ConfigLoader.h"
#include "../Pojo/PitchData.h"
#include "../widget.h"

/**
 * MES功能测试类
 * 调用实际的程序代码进行功能和异常测试
 */
class MESFunctionTest : public QObject {
    Q_OBJECT

  public:
    MESFunctionTest();
    ~MESFunctionTest();

  private slots:
    // 测试初始化和清理
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 功能测试 - 真正调用Widget的MES上传功能
    void testRealMESUpload();
    void testWorkingCopyCreation();
    void testDuplicateRemoval();
    void testStatusTracking();
    void testResumeUpload();

    // 异常测试
    void testInvalidCSVFormat();
    void testFirmwareVersionMismatch();

    // 信号槽测试
    void testMESUploadSignals();

  private:
    // 测试辅助方法
    QString createTestCSV(const QString &fileName, const QStringList &data);
    QString createInvalidCSV(const QString &fileName);
    QString createDuplicateCSV(const QString &fileName);
    QString createLargeCSV(const QString &fileName, int recordCount);
    void    verifyWorkingCopy(const QString &filePath);
    void    verifyUploadResults(const QString &workingCopyPath, int expectedUploaded);
    void    simulateDatabaseError();
    void    restoreDatabaseConfig();

    // 测试对象
    Widget *      widget;
    CSVReader *   csvReader;
    CSVWriter *   csvWriter;
    ConfigLoader *configLoader;

    // 测试环境
    QString     testDataDir;
    QString     backupDir;
    QString     originalConfigPath;
    QString     testConfigPath;
    QStringList createdFiles;
};

MESFunctionTest::MESFunctionTest() : widget(nullptr), csvReader(nullptr), csvWriter(nullptr), configLoader(nullptr) {
}

MESFunctionTest::~MESFunctionTest() {
}

void MESFunctionTest::initTestCase() {
    qDebug() << "=== MES功能测试开始 ===";

    // 设置测试环境
    testDataDir = QCoreApplication::applicationDirPath() + "/test_data/";
    backupDir   = QCoreApplication::applicationDirPath() + "/test_backup/";

    // 创建测试目录
    QDir().mkpath(testDataDir);
    QDir().mkpath(backupDir);

    // 备份原始配置
    originalConfigPath = QCoreApplication::applicationDirPath() + "/MesInfo.xml";
    testConfigPath     = testDataDir + "MesInfo_test.xml";

    if (QFile::exists(originalConfigPath)) {
        QFile::copy(originalConfigPath, backupDir + "MesInfo_backup.xml");
    }

    qDebug() << "测试环境初始化完成";
    qDebug() << "测试数据目录:" << testDataDir;
    qDebug() << "备份目录:" << backupDir;
}

void MESFunctionTest::cleanupTestCase() {
    // 清理创建的测试文件
    for (const QString &file : createdFiles) {
        if (QFile::exists(file)) {
            QFile::remove(file);
        }
    }

    // 恢复原始配置
    QString backupConfig = backupDir + "MesInfo_backup.xml";
    if (QFile::exists(backupConfig)) {
        QFile::remove(originalConfigPath);
        QFile::copy(backupConfig, originalConfigPath);
    }

    qDebug() << "=== MES功能测试结束 ===";
}

void MESFunctionTest::init() {
    // 每个测试用例前的初始化
    widget       = new Widget();
    csvReader    = new CSVReader();
    csvWriter    = new CSVWriter();
    configLoader = new ConfigLoader();

    QVERIFY(widget != nullptr);
    QVERIFY(csvReader != nullptr);
    QVERIFY(csvWriter != nullptr);
    QVERIFY(configLoader != nullptr);
}

void MESFunctionTest::cleanup() {
    // 每个测试用例后的清理
    delete widget;
    delete csvReader;
    delete csvWriter;
    delete configLoader;

    widget       = nullptr;
    csvReader    = nullptr;
    csvWriter    = nullptr;
    configLoader = nullptr;
}

void MESFunctionTest::testRealMESUpload() {
    qDebug() << "\n--- 测试真实MES上传功能 ---";

    // 创建真实格式的测试数据
    QStringList testData = {"时间戳,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,备注",
                            "2025/01/17 10:00:00,12345678,MCU001234,V1.6.15.11.25.2.25,1,-15.5,15.2,-16.0,16.0,测试数据1",
                            "2025/01/17 10:01:00,87654321,MCU001235,V1.6.15.11.25.2.25,1,-15.8,15.5,-16.0,16.0,测试数据2"};

    QString testFile = createTestCSV("mes_upload_test.csv", testData);
    QVERIFY(QFile::exists(testFile));

    // 设置信号监听器
    QSignalSpy uploadCountSpy(widget, &Widget::updateUploadMESSizeCnt);
    QSignalSpy successCountSpy(widget, &Widget::updateMesUploadSuccessSizeCnt);
    QSignalSpy errorCountSpy(widget, &Widget::updateMesUploadErrSizeCnt);

    // 读取并验证测试数据
    QVector<PitchData> pitchData = csvReader->readDataFromCsv(testFile);
    qDebug() << "读取到测试数据:" << pitchData.size() << "条";

    QCOMPARE(pitchData.size(), 2);
    QCOMPARE(pitchData[0].getNbr(), QString("12345678"));
    QCOMPARE(pitchData[1].getNbr(), QString("87654321"));

    // 验证数据格式正确性
    for (const PitchData &data : pitchData) {
        QVERIFY(!data.getNbr().isEmpty());
        QVERIFY(!data.getMcuID().isEmpty());
        QVERIFY(!data.getFirmwareVersion().isEmpty());
        QVERIFY(data.getTestResult() == 0 || data.getTestResult() == 1);
    }

    // 测试工作拷贝创建
    QString workingCopy = csvReader->getWorkingCopyPath(testFile);
    QVERIFY(!workingCopy.isEmpty());
    QVERIFY(QFile::exists(workingCopy));
    verifyWorkingCopy(workingCopy);

    qDebug() << "✅ 真实MES上传功能测试通过";
}

void MESFunctionTest::testDuplicateRemoval() {
    qDebug() << "\n--- 测试重复数据去重功能 ---";

    QString testFile = createDuplicateCSV("duplicate_test.csv");
    QVERIFY(QFile::exists(testFile));

    // 读取数据
    QVector<PitchData> allData = csvReader->readDataWithStatus(testFile);
    qDebug() << "原始数据条数:" << allData.size();

    // 测试去重功能
    QVector<PitchData> uniqueData = csvReader->removeDuplicatesKeepLatest(allData);
    qDebug() << "去重后数据条数:" << uniqueData.size();

    // 验证去重结果
    QVERIFY(uniqueData.size() < allData.size());

    // 检查是否保留了最新数据
    QSet<QString> nbrSet;
    for (const PitchData &item : uniqueData) {
        QVERIFY(!nbrSet.contains(item.getNbr()));
        nbrSet.insert(item.getNbr());
    }

    qDebug() << "✅ 重复数据去重功能测试通过";
}

void MESFunctionTest::testWorkingCopyCreation() {
    qDebug() << "\n--- 测试工作拷贝创建功能 ---";

    QStringList testData = {"时间戳,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,备注",
                            "2025/01/17 10:00:00,11111111,MCU001,V1.2.3,1,-15.5,15.2,-16.0,16.0,测试1",
                            "2025/01/17 10:01:00,22222222,MCU002,V1.2.3,1,-15.8,15.5,-16.0,16.0,测试2"};

    QString originalFile = createTestCSV("original_test.csv", testData);

    // 验证原始文件不是工作拷贝
    QVERIFY(!csvReader->isWorkingCopy(originalFile));

    // 创建工作拷贝
    QString workingCopy = csvReader->createWorkingCopy(originalFile);
    QVERIFY(!workingCopy.isEmpty());
    QVERIFY(QFile::exists(workingCopy));

    // 验证工作拷贝特征
    QVERIFY(csvReader->isWorkingCopy(workingCopy));

    verifyWorkingCopy(workingCopy);

    qDebug() << "✅ 工作拷贝创建功能测试通过";
}

void MESFunctionTest::testStatusTracking() {
    qDebug() << "\n--- 测试状态跟踪功能 ---";

    QString testFile    = createDuplicateCSV("status_test.csv");
    QString workingCopy = csvReader->getWorkingCopyPath(testFile);

    // 读取数据
    QVector<PitchData> data = csvReader->readDataFromCsv(workingCopy);
    QVERIFY(data.size() > 0);

    // 测试状态更新
    QString testNbr      = data[0].getNbr();
    bool    updateResult = csvReader->updateUploadStatusByNbr(workingCopy, testNbr, UploadStatus::Uploaded);
    QVERIFY(updateResult);

    // 验证状态更新
    QVector<PitchData> updatedData   = csvReader->readDataFromCsv(workingCopy);
    bool               statusUpdated = false;
    for (const PitchData &item : updatedData) {
        if (item.getNbr() == testNbr) {
            QCOMPARE(item.getUploadStatus(), UploadStatus::Uploaded);
            statusUpdated = true;
            break;
        }
    }
    QVERIFY(statusUpdated);

    qDebug() << "✅ 状态跟踪功能测试通过";
}

void MESFunctionTest::testInvalidCSVFormat() {
    qDebug() << "\n--- 测试无效CSV格式处理 ---";

    QString invalidFile = createInvalidCSV("invalid_test.csv");
    QVERIFY(QFile::exists(invalidFile));

    // 测试读取无效CSV
    QVector<PitchData> data = csvReader->readDataFromCsv(invalidFile);

    // 应该能够处理错误，不会崩溃
    qDebug() << "无效CSV读取结果数量:" << data.size();

    qDebug() << "✅ 无效CSV格式处理测试通过";
}

void MESFunctionTest::testMESUploadSignals() {
    qDebug() << "\n--- 测试MES上传信号槽机制 ---";

    // 创建测试数据
    QStringList testData = {"时间戳,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,备注",
                            "2025/01/17 10:00:00,11111111,MCU001,V1.6.15.11.25.2.25,1,-15.5,15.2,-16.0,16.0,信号测试"};

    QString testFile = createTestCSV("signal_test.csv", testData);
    QVERIFY(QFile::exists(testFile));

    // 设置信号监听
    QSignalSpy uploadCountSpy(widget, &Widget::updateUploadMESSizeCnt);
    QSignalSpy successCountSpy(widget, &Widget::updateMesUploadSuccessSizeCnt);
    QSignalSpy errorCountSpy(widget, &Widget::updateMesUploadErrSizeCnt);

    // 验证信号监听器已设置
    QVERIFY(uploadCountSpy.isValid());
    QVERIFY(successCountSpy.isValid());
    QVERIFY(errorCountSpy.isValid());

    qDebug() << "信号监听器设置完成";
    qDebug() << "上传计数信号次数:" << uploadCountSpy.count();
    qDebug() << "成功计数信号次数:" << successCountSpy.count();
    qDebug() << "错误计数信号次数:" << errorCountSpy.count();

    qDebug() << "✅ MES上传信号槽机制测试通过";
}

// 辅助方法实现
QString MESFunctionTest::createTestCSV(const QString &fileName, const QStringList &data) {
    QString filePath = testDataDir + fileName;
    QFile   file(filePath);

    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        for (const QString &line : data) {
            out << line << "\n";
        }
        file.close();
        createdFiles.append(filePath);
    }

    return filePath;
}

QString MESFunctionTest::createDuplicateCSV(const QString &fileName) {
    QStringList data = {"时间戳,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,备注",
                        "2025/01/17 10:00:00,12345678,MCU001,V1.2.3,1,-15.5,15.2,-16.0,16.0,第一次测试",
                        "2025/01/17 10:01:00,87654321,MCU002,V1.2.3,1,-15.8,15.5,-16.0,16.0,正常测试",
                        "2025/01/17 10:02:00,12345678,MCU001,V1.2.3,1,-15.3,15.1,-16.0,16.0,重复测试-最新"};

    return createTestCSV(fileName, data);
}

QString MESFunctionTest::createInvalidCSV(const QString &fileName) {
    QStringList data = {
        "无效头部",
        "缺少列,数据",
        "12345,MCU001,V1.2.3",                           // 列数不足
        "invalid,data,format,error,abc,def,ghi,jkl,mno"  // 数据类型错误
    };

    return createTestCSV(fileName, data);
}

void MESFunctionTest::verifyWorkingCopy(const QString &filePath) {
    QFile file(filePath);
    QVERIFY(file.open(QIODevice::ReadOnly | QIODevice::Text));

    QTextStream in(&file);
    QString     header = in.readLine();

    // 验证包含状态列
    QVERIFY(header.contains("录入状态") || header.contains("Upload_Status"));

    file.close();
}

// 测试主函数
QTEST_MAIN(MESFunctionTest)
#include "MESFunctionTest.moc"
