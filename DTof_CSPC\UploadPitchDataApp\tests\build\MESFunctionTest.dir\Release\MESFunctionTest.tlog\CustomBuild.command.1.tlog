^F:\13_YAPHA-LASER-DTOF2DMS\DEVELOPMENT\TOOL\YAPHA-PROJ-MES-02\DTOF_CSPC\UPLOADPITCHDATAAPP\WIDGET.UI
setlocal
D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe -o F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/ui_widget.h F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/widget.ui
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\13_YAPHA-LASER-DTOF2DMS\DEVELOPMENT\TOOL\YAPHA-PROJ-MES-02\DTOF_CSPC\UPLOADPITCHDATAAPP\COMM.UI
setlocal
D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe -o F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/ui_Comm.h F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Comm.ui
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\13_YAPHA-LASER-DTOF2DMS\DEVELOPMENT\TOOL\YAPHA-PROJ-MES-02\DTOF_CSPC\UPLOADPITCHDATAAPP\MAINFORM.UI
setlocal
D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe -o F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/ui_MainForm.h F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/MainForm.ui
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\13_YAPHA-LASER-DTOF2DMS\DEVELOPMENT\TOOL\YAPHA-PROJ-MES-02\DTOF_CSPC\UPLOADPITCHDATAAPP\TESTS\CMAKELISTS.TXT
setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests -BF:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
