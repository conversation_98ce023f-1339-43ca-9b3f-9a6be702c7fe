#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QEventLoop>
#include <QFileInfo>
#include <QSignalSpy>
#include <QThread>
#include <QTimer>
#include <QtTest/QtTest>

#include "../CSV/CSVReader.h"
#include "../CSV/CSVWriter.h"
#include "../Config/ConfigLoader.h"
#include "../Pojo/PitchData.h"
#include "../widget.h"
#include "MockMESUploader.h"
#include "TestDataGenerator.h"

/**
 * @brief MES模拟测试类
 *
 * 使用模拟信号和数据生成器测试MES上传功能的完整流程
 * 包括正常上传、异常处理、性能测试等场景
 */
class MESSimulationTest : public QObject {
    Q_OBJECT

  public:
    MESSimulationTest();
    ~MESSimulationTest();

  private slots:
    // 测试初始化和清理
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 核心功能测试
    void testNormalMESUploadSimulation();
    void testDuplicateDataHandling();
    void testWorkingCopyManagement();
    void testStatusTrackingSimulation();
    void testSignalEmissionVerification();

    // 异常情况测试
    void testNetworkInterruptionSimulation();
    void testDatabaseConnectionFailure();
    void testInvalidDataHandling();
    void testLargeDataSetProcessing();

    // 性能测试
    void testUploadPerformance();
    void testConcurrentUploadSimulation();
    void testMemoryUsageMonitoring();

    // 集成测试
    void testCompleteWorkflowSimulation();
    void testResumeUploadAfterFailure();

  private:
    // 测试辅助方法
    void setupTestEnvironment();
    void cleanupTestFiles();
    void verifyUploadStatistics(int expectedTotal, int expectedSuccess, int expectedFailure);
    void simulateUserInteraction();
    void waitForUploadCompletion(int timeoutMs = 10000);

    // 测试对象
    Widget *           m_widget;
    CSVReader *        m_csvReader;
    CSVWriter *        m_csvWriter;
    TestDataGenerator *m_dataGenerator;
    MockMESUploader *  m_mockUploader;

    // 测试环境
    QString     m_testDataDir;
    QString     m_backupDir;
    QStringList m_createdFiles;

    // 测试配置
    QString m_originalConfigPath;
    QString m_testConfigPath;
};

MESSimulationTest::MESSimulationTest() : m_widget(nullptr), m_csvReader(nullptr), m_csvWriter(nullptr), m_dataGenerator(nullptr), m_mockUploader(nullptr) {
}

MESSimulationTest::~MESSimulationTest() {
}

void MESSimulationTest::initTestCase() {
    qDebug() << "=== MES模拟测试开始 ===";
    qDebug() << "测试目标：使用模拟信号验证MES上传功能的完整性";

    setupTestEnvironment();

    qDebug() << "测试环境初始化完成";
    qDebug() << "- 测试数据目录:" << m_testDataDir;
    qDebug() << "- 备份目录:" << m_backupDir;
}

void MESSimulationTest::cleanupTestCase() {
    cleanupTestFiles();

    // 恢复原始配置
    QString backupConfig = m_backupDir + "MesInfo_backup.xml";
    if (QFile::exists(backupConfig)) {
        QFile::remove(m_originalConfigPath);
        QFile::copy(backupConfig, m_originalConfigPath);
    }

    qDebug() << "=== MES模拟测试结束 ===";
}

void MESSimulationTest::init() {
    // 每个测试用例前的初始化
    m_widget        = new Widget();
    m_csvReader     = new CSVReader();
    m_csvWriter     = new CSVWriter();
    m_dataGenerator = new TestDataGenerator();
    m_mockUploader  = new MockMESUploader();

    // 设置测试数据生成器
    m_dataGenerator->setOutputDirectory(m_testDataDir);
    m_dataGenerator->setFirmwareVersion("V1.6.15.11.25.2.25");

    // 设置模拟上传器
    m_mockUploader->setSuccessRate(0.95);      // 95%成功率
    m_mockUploader->setNetworkDelay(50, 200);  // 50-200ms延迟
    m_mockUploader->resetStatistics();

    QVERIFY(m_widget != nullptr);
    QVERIFY(m_csvReader != nullptr);
    QVERIFY(m_csvWriter != nullptr);
    QVERIFY(m_dataGenerator != nullptr);
    QVERIFY(m_mockUploader != nullptr);
}

void MESSimulationTest::cleanup() {
    // 每个测试用例后的清理
    delete m_widget;
    delete m_csvReader;
    delete m_csvWriter;
    delete m_dataGenerator;
    delete m_mockUploader;

    m_widget        = nullptr;
    m_csvReader     = nullptr;
    m_csvWriter     = nullptr;
    m_dataGenerator = nullptr;
    m_mockUploader  = nullptr;
}

void MESSimulationTest::testNormalMESUploadSimulation() {
    qDebug() << "\n--- 测试正常MES上传模拟 ---";

    // 生成测试数据
    QString testFile = m_dataGenerator->generateNormalCSV("normal_upload_test.csv", 5);
    QVERIFY(!testFile.isEmpty());
    QVERIFY(QFile::exists(testFile));
    m_createdFiles.append(testFile);

    // 设置信号监听器
    QSignalSpy uploadCountSpy(m_widget, &Widget::updateUploadMESSizeCnt);
    QSignalSpy successCountSpy(m_widget, &Widget::updateMesUploadSuccessSizeCnt);
    QSignalSpy errorCountSpy(m_widget, &Widget::updateMesUploadErrSizeCnt);

    // 读取测试数据
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    qDebug() << "读取到测试数据:" << pitchData.size() << "条";
    QCOMPARE(pitchData.size(), 5);

    // 模拟上传过程
    m_mockUploader->uploadPitchData(pitchData);

    // 等待上传完成
    waitForUploadCompletion(5000);

    // 验证上传统计
    int totalUploaded = m_mockUploader->getTotalUploaded();
    int successCount  = m_mockUploader->getSuccessCount();
    int failureCount  = m_mockUploader->getFailureCount();

    qDebug() << "上传统计 - 总数:" << totalUploaded << "成功:" << successCount << "失败:" << failureCount;

    QCOMPARE(totalUploaded, 5);
    QVERIFY(successCount >= 4);  // 至少80%成功率
    QVERIFY(successCount + failureCount == totalUploaded);

    qDebug() << "✅ 正常MES上传模拟测试通过";
}

void MESSimulationTest::testDuplicateDataHandling() {
    qDebug() << "\n--- 测试重复数据处理模拟 ---";

    // 生成包含重复数据的测试文件
    QString testFile = m_dataGenerator->generateDuplicateCSV("duplicate_test.csv", 3);
    QVERIFY(!testFile.isEmpty());
    QVERIFY(QFile::exists(testFile));
    m_createdFiles.append(testFile);

    // 读取数据
    QVector<PitchData> allData = m_csvReader->readDataWithStatus(testFile);
    qDebug() << "原始数据条数:" << allData.size();

    // 测试去重功能
    QVector<PitchData> uniqueData = m_csvReader->removeDuplicatesKeepLatest(allData);
    qDebug() << "去重后数据条数:" << uniqueData.size();

    // 验证去重结果
    QVERIFY(uniqueData.size() < allData.size());

    // 模拟上传去重后的数据
    m_mockUploader->uploadPitchData(uniqueData);
    waitForUploadCompletion(3000);

    // 验证上传结果
    int totalUploaded = m_mockUploader->getTotalUploaded();
    QCOMPARE(totalUploaded, uniqueData.size());

    qDebug() << "✅ 重复数据处理模拟测试通过";
}

void MESSimulationTest::testWorkingCopyManagement() {
    qDebug() << "\n--- 测试工作拷贝管理模拟 ---";

    // 生成原始测试数据
    QString originalFile = m_dataGenerator->generateNormalCSV("original_file.csv", 3);
    QVERIFY(!originalFile.isEmpty());
    m_createdFiles.append(originalFile);

    // 验证原始文件不是工作拷贝
    QVERIFY(!m_csvReader->isWorkingCopy(originalFile));

    // 创建工作拷贝
    QString workingCopy = m_csvReader->createWorkingCopy(originalFile);
    QVERIFY(!workingCopy.isEmpty());
    QVERIFY(QFile::exists(workingCopy));
    QVERIFY(m_csvReader->isWorkingCopy(workingCopy));
    m_createdFiles.append(workingCopy);

    // 读取工作拷贝数据
    QVector<PitchData> data = m_csvReader->readDataFromCsv(workingCopy);
    QVERIFY(data.size() > 0);

    // 测试状态更新
    QString testNbr      = data[0].getNbr();
    bool    updateResult = m_csvReader->updateUploadStatusByNbr(workingCopy, testNbr, UploadStatus::Uploaded);
    QVERIFY(updateResult);

    // 验证状态更新
    QVector<PitchData> updatedData   = m_csvReader->readDataFromCsv(workingCopy);
    bool               statusUpdated = false;
    for (const PitchData &item : updatedData) {
        if (item.getNbr() == testNbr) {
            QCOMPARE(item.getUploadStatus(), UploadStatus::Uploaded);
            statusUpdated = true;
            break;
        }
    }
    QVERIFY(statusUpdated);

    qDebug() << "✅ 工作拷贝管理模拟测试通过";
}

void MESSimulationTest::testSignalEmissionVerification() {
    qDebug() << "\n--- 测试信号发射验证 ---";

    // 生成测试数据
    QString testFile = m_dataGenerator->generateNormalCSV("signal_test.csv", 3);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 设置信号监听器
    QSignalSpy uploadStartedSpy(m_mockUploader, &MockMESUploader::uploadStarted);
    QSignalSpy uploadCompletedSpy(m_mockUploader, &MockMESUploader::uploadCompleted);
    QSignalSpy statisticsUpdatedSpy(m_mockUploader, &MockMESUploader::statisticsUpdated);

    // 验证信号监听器设置
    QVERIFY(uploadStartedSpy.isValid());
    QVERIFY(uploadCompletedSpy.isValid());
    QVERIFY(statisticsUpdatedSpy.isValid());

    // 读取数据并模拟上传
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    m_mockUploader->uploadPitchData(pitchData);

    // 等待上传完成
    waitForUploadCompletion(3000);

    // 验证信号发射
    qDebug() << "上传开始信号次数:" << uploadStartedSpy.count();
    qDebug() << "上传完成信号次数:" << uploadCompletedSpy.count();
    qDebug() << "统计更新信号次数:" << statisticsUpdatedSpy.count();

    QCOMPARE(uploadStartedSpy.count(), pitchData.size());
    QCOMPARE(uploadCompletedSpy.count(), pitchData.size());
    QVERIFY(statisticsUpdatedSpy.count() >= 1);

    qDebug() << "✅ 信号发射验证测试通过";
}

void MESSimulationTest::testNetworkInterruptionSimulation() {
    qDebug() << "\n--- 测试网络中断模拟 ---";

    // 生成测试数据
    QString testFile = m_dataGenerator->generateNormalCSV("network_test.csv", 5);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 设置网络中断监听
    QSignalSpy networkInterruptedSpy(m_mockUploader, &MockMESUploader::networkInterrupted);

    // 模拟网络中断
    m_mockUploader->simulateNetworkInterruption(2000);  // 2秒中断

    // 验证网络中断信号
    QCOMPARE(networkInterruptedSpy.count(), 1);

    // 在网络中断期间尝试上传
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    m_mockUploader->uploadPitchData(pitchData);

    // 等待网络恢复和上传完成
    waitForUploadCompletion(5000);

    // 验证失败统计
    int failureCount = m_mockUploader->getFailureCount();
    qDebug() << "网络中断期间失败数:" << failureCount;
    QVERIFY(failureCount > 0);  // 应该有失败记录

    qDebug() << "✅ 网络中断模拟测试通过";
}

void MESSimulationTest::testLargeDataSetProcessing() {
    qDebug() << "\n--- 测试大数据集处理 ---";

    // 生成大数据集
    QString testFile = m_dataGenerator->generateLargeCSV("large_dataset.csv", 100);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 记录开始时间
    QDateTime startTime = QDateTime::currentDateTime();

    // 读取大数据集
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    qDebug() << "大数据集读取完成，数据量:" << pitchData.size();
    QCOMPARE(pitchData.size(), 100);

    // 启用批量上传模式
    m_mockUploader->setBatchUploadMode(true);

    // 模拟上传
    m_mockUploader->uploadPitchData(pitchData);
    waitForUploadCompletion(15000);  // 15秒超时

    // 计算处理时间
    QDateTime endTime        = QDateTime::currentDateTime();
    qint64    processingTime = startTime.msecsTo(endTime);
    qDebug() << "大数据集处理时间:" << processingTime << "ms";

    // 验证处理结果
    int totalUploaded = m_mockUploader->getTotalUploaded();
    QCOMPARE(totalUploaded, 100);

    // 性能要求：100条记录应在15秒内完成
    QVERIFY(processingTime < 15000);

    qDebug() << "✅ 大数据集处理测试通过";
}

// 私有辅助方法实现
void MESSimulationTest::setupTestEnvironment() {
    // 设置测试目录
    m_testDataDir = QCoreApplication::applicationDirPath() + "/test_simulation_data/";
    m_backupDir   = QCoreApplication::applicationDirPath() + "/test_backup/";

    // 创建测试目录
    QDir().mkpath(m_testDataDir);
    QDir().mkpath(m_backupDir);

    // 备份原始配置
    m_originalConfigPath = QCoreApplication::applicationDirPath() + "/MesInfo.xml";
    m_testConfigPath     = m_testDataDir + "MesInfo_test.xml";

    if (QFile::exists(m_originalConfigPath)) {
        QFile::copy(m_originalConfigPath, m_backupDir + "MesInfo_backup.xml");
    }
}

void MESSimulationTest::cleanupTestFiles() {
    // 清理创建的测试文件
    for (const QString &file : m_createdFiles) {
        if (QFile::exists(file)) {
            QFile::remove(file);
            qDebug() << "删除测试文件:" << file;
        }
    }
    m_createdFiles.clear();
}

void MESSimulationTest::waitForUploadCompletion(int timeoutMs) {
    QEventLoop loop;
    QTimer     timer;
    timer.setSingleShot(true);
    timer.setInterval(timeoutMs);

    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start();

    // 等待上传完成或超时
    loop.exec();
}

void MESSimulationTest::testStatusTrackingSimulation() {
    qDebug() << "\n--- 测试状态跟踪模拟 ---";

    // 生成测试数据
    QString testFile = m_dataGenerator->generateNormalCSV("status_tracking_test.csv", 3);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 创建工作拷贝
    QString workingCopy = m_csvReader->createWorkingCopy(testFile);
    QVERIFY(!workingCopy.isEmpty());
    m_createdFiles.append(workingCopy);

    // 读取数据
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(workingCopy);
    QVERIFY(pitchData.size() == 3);

    // 验证初始状态
    for (const PitchData &data : pitchData) {
        QCOMPARE(data.getUploadStatus(), UploadStatus::NotUploaded);
    }

    // 模拟状态更新
    QString firstNbr     = pitchData[0].getNbr();
    bool    updateResult = m_csvReader->updateUploadStatusByNbr(workingCopy, firstNbr, UploadStatus::Uploaded);
    QVERIFY(updateResult);

    // 重新读取并验证状态更新
    QVector<PitchData> updatedData = m_csvReader->readDataFromCsv(workingCopy);
    bool               statusFound = false;
    for (const PitchData &data : updatedData) {
        if (data.getNbr() == firstNbr) {
            QCOMPARE(data.getUploadStatus(), UploadStatus::Uploaded);
            statusFound = true;
            break;
        }
    }
    QVERIFY(statusFound);

    qDebug() << "✅ 状态跟踪模拟测试通过";
}

void MESSimulationTest::testDatabaseConnectionFailure() {
    qDebug() << "\n--- 测试数据库连接失败模拟 ---";

    // 设置数据库连接失败模拟
    m_mockUploader->setDatabaseConnectionFailure(true);

    // 生成测试数据
    QString testFile = m_dataGenerator->generateNormalCSV("db_failure_test.csv", 2);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 读取数据并模拟上传
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    m_mockUploader->uploadPitchData(pitchData);

    waitForUploadCompletion(3000);

    // 验证失败统计
    int failureCount = m_mockUploader->getFailureCount();
    qDebug() << "数据库连接失败期间失败数:" << failureCount;
    QVERIFY(failureCount > 0);

    // 恢复数据库连接
    m_mockUploader->setDatabaseConnectionFailure(false);

    qDebug() << "✅ 数据库连接失败模拟测试通过";
}

void MESSimulationTest::testInvalidDataHandling() {
    qDebug() << "\n--- 测试无效数据处理 ---";

    // 生成无效数据
    QString invalidFile = m_dataGenerator->generateInvalidCSV("invalid_data_test.csv", TestDataGenerator::ExceptionType::InvalidFormat);
    QVERIFY(!invalidFile.isEmpty());
    m_createdFiles.append(invalidFile);

    // 尝试读取无效数据
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(invalidFile);
    qDebug() << "无效数据读取结果:" << pitchData.size() << "条";

    // 无效数据应该被过滤掉或处理为空
    QVERIFY(pitchData.size() == 0 || pitchData.size() < 5);

    qDebug() << "✅ 无效数据处理测试通过";
}

void MESSimulationTest::testUploadPerformance() {
    qDebug() << "\n--- 测试上传性能 ---";

    // 生成中等数据量
    QString testFile = m_dataGenerator->generateNormalCSV("performance_test.csv", 50);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 记录开始时间
    QDateTime startTime = QDateTime::currentDateTime();

    // 读取数据并上传
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    QCOMPARE(pitchData.size(), 50);

    m_mockUploader->setBatchUploadMode(true);
    m_mockUploader->uploadPitchData(pitchData);

    waitForUploadCompletion(10000);

    // 计算处理时间
    QDateTime endTime        = QDateTime::currentDateTime();
    qint64    processingTime = startTime.msecsTo(endTime);

    qDebug() << "50条记录处理时间:" << processingTime << "ms";
    qDebug() << "平均每条记录处理时间:" << (processingTime / 50.0) << "ms";

    // 性能要求：50条记录应在10秒内完成
    QVERIFY(processingTime < 10000);

    qDebug() << "✅ 上传性能测试通过";
}

void MESSimulationTest::testConcurrentUploadSimulation() {
    qDebug() << "\n--- 测试并发上传模拟 ---";

    // 生成多个小数据集
    QString testFile1 = m_dataGenerator->generateNormalCSV("concurrent_test1.csv", 3);
    QString testFile2 = m_dataGenerator->generateNormalCSV("concurrent_test2.csv", 3);
    QVERIFY(!testFile1.isEmpty() && !testFile2.isEmpty());
    m_createdFiles.append(testFile1);
    m_createdFiles.append(testFile2);

    // 读取数据
    QVector<PitchData> pitchData1 = m_csvReader->readDataFromCsv(testFile1);
    QVector<PitchData> pitchData2 = m_csvReader->readDataFromCsv(testFile2);

    // 重置统计
    m_mockUploader->resetStatistics();

    // 模拟并发上传（实际上是顺序执行，但测试并发处理能力）
    m_mockUploader->uploadPitchData(pitchData1);
    m_mockUploader->uploadPitchData(pitchData2);

    waitForUploadCompletion(5000);

    // 验证总上传数量
    int totalUploaded = m_mockUploader->getTotalUploaded();
    qDebug() << "并发上传总数:" << totalUploaded;
    QCOMPARE(totalUploaded, 6);  // 3 + 3

    qDebug() << "✅ 并发上传模拟测试通过";
}

void MESSimulationTest::testMemoryUsageMonitoring() {
    qDebug() << "\n--- 测试内存使用监控 ---";

    // 生成大数据集
    QString testFile = m_dataGenerator->generateLargeCSV("memory_test.csv", 200);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 读取大数据集
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(testFile);
    QCOMPARE(pitchData.size(), 200);

    // 模拟处理（这里主要验证不会内存溢出）
    m_mockUploader->setBatchUploadMode(true);
    m_mockUploader->uploadPitchData(pitchData);

    waitForUploadCompletion(15000);

    // 验证处理完成
    int totalUploaded = m_mockUploader->getTotalUploaded();
    qDebug() << "大数据集处理完成，总数:" << totalUploaded;
    QCOMPARE(totalUploaded, 200);

    qDebug() << "✅ 内存使用监控测试通过";
}

void MESSimulationTest::testCompleteWorkflowSimulation() {
    qDebug() << "\n--- 测试完整工作流模拟 ---";

    // 1. 生成包含重复数据的测试文件
    QString testFile = m_dataGenerator->generateDuplicateCSV("workflow_test.csv", 2);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 2. 读取原始数据
    QVector<PitchData> originalData  = m_csvReader->readDataFromCsv(testFile);
    int                originalCount = originalData.size();
    qDebug() << "原始数据量:" << originalCount;

    // 3. 创建工作拷贝
    QString workingCopy = m_csvReader->createWorkingCopy(testFile);
    QVERIFY(!workingCopy.isEmpty());
    m_createdFiles.append(workingCopy);

    // 4. 去重处理
    QVector<PitchData> uniqueData  = m_csvReader->removeDuplicatesKeepLatest(originalData);
    int                uniqueCount = uniqueData.size();
    qDebug() << "去重后数据量:" << uniqueCount;
    QVERIFY(uniqueCount <= originalCount);

    // 5. 模拟上传
    m_mockUploader->resetStatistics();
    m_mockUploader->uploadPitchData(uniqueData);
    waitForUploadCompletion(5000);

    // 6. 验证结果
    int totalUploaded = m_mockUploader->getTotalUploaded();
    int successCount  = m_mockUploader->getSuccessCount();

    qDebug() << "完整工作流结果:";
    qDebug() << "- 总上传数:" << totalUploaded;
    qDebug() << "- 成功数:" << successCount;
    qDebug() << "- 成功率:" << m_mockUploader->getCurrentSuccessRate();

    QCOMPARE(totalUploaded, uniqueCount);
    QVERIFY(successCount >= totalUploaded * 0.8);  // 至少80%成功率

    qDebug() << "✅ 完整工作流模拟测试通过";
}

void MESSimulationTest::testResumeUploadAfterFailure() {
    qDebug() << "\n--- 测试失败后恢复上传 ---";

    // 生成测试数据
    QString testFile = m_dataGenerator->generateNormalCSV("resume_after_failure_test.csv", 5);
    QVERIFY(!testFile.isEmpty());
    m_createdFiles.append(testFile);

    // 创建工作拷贝
    QString workingCopy = m_csvReader->createWorkingCopy(testFile);
    QVERIFY(!workingCopy.isEmpty());
    m_createdFiles.append(workingCopy);

    // 读取数据
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(workingCopy);
    QVERIFY(pitchData.size() == 5);

    // 模拟部分成功，部分失败
    QString firstNbr  = pitchData[0].getNbr();
    QString secondNbr = pitchData[1].getNbr();
    QString thirdNbr  = pitchData[2].getNbr();

    // 标记前两条为成功
    m_csvReader->updateUploadStatusByNbr(workingCopy, firstNbr, UploadStatus::Uploaded);
    m_csvReader->updateUploadStatusByNbr(workingCopy, secondNbr, UploadStatus::Uploaded);

    // 标记第三条为失败
    m_csvReader->updateUploadStatusByNbr(workingCopy, thirdNbr, UploadStatus::Failed);

    // 重新读取并验证状态
    QVector<PitchData> updatedData = m_csvReader->readDataFromCsv(workingCopy);

    int uploadedCount = 0;
    int failedCount   = 0;
    int pendingCount  = 0;

    for (const PitchData &data : updatedData) {
        switch (data.getUploadStatus()) {
        case UploadStatus::Uploaded:
            uploadedCount++;
            break;
        case UploadStatus::Failed:
            failedCount++;
            break;
        case UploadStatus::NotUploaded:
            pendingCount++;
            break;
        }
    }

    qDebug() << "恢复前状态统计:";
    qDebug() << "- 已上传:" << uploadedCount;
    qDebug() << "- 失败:" << failedCount;
    qDebug() << "- 待处理:" << pendingCount;

    QCOMPARE(uploadedCount, 2);
    QCOMPARE(failedCount, 1);
    QCOMPARE(pendingCount, 2);

    qDebug() << "✅ 失败后恢复上传测试通过";
}

// 测试主函数
QTEST_MAIN(MESSimulationTest)
#include "MESSimulationTest.moc"
