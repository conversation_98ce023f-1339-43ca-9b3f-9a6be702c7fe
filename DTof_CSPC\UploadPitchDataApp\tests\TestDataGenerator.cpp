#include "TestDataGenerator.h"

TestDataGenerator::TestDataGenerator(QObject *parent)
    : QObject(parent),
      m_outputDirectory("./test_data/"),
      m_firmwareVersion("V1.6.15.11.25.2.25"),
      m_startDateTime(QDateTime::currentDateTime().addDays(-1)),
      m_endDateTime(QDateTime::currentDateTime()),
      m_minAngle(-16.0f),
      m_maxAngle(16.0f),
      m_minStandardAngle(-16.0f),
      m_maxStandardAngle(16.0f) {
    // 确保输出目录存在
    QDir().mkpath(m_outputDirectory);
    qDebug() << "TestDataGenerator initialized with output directory:" << m_outputDirectory;
}

TestDataGenerator::~TestDataGenerator() {
    qDebug() << "TestDataGenerator destroyed. Generated files count:" << m_generatedFiles.size();
}

QString TestDataGenerator::generateNormalCSV(const QString &fileName, int recordCount) {
    qDebug() << "生成正常CSV文件:" << fileName << "记录数:" << recordCount;

    QString     filePath = m_outputDirectory + fileName;
    QStringList headers = {"时间戳", "电机标签", "MCUID", "固件版本", "测试结果", "最小俯仰角", "最大俯仰角", "俯仰角下限", "俯仰角上限", "备注"};

    QStringList dataLines;
    dataLines.append(headers.join(","));

    for (int i = 0; i < recordCount; ++i) {
        QStringList record;
        record << generateTimestamp() << generateMotorLabel() << generateMCUID() << m_firmwareVersion << QString::number(generateTestResult())
               << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1) << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1)
               << QString::number(m_minStandardAngle, 'f', 1) << QString::number(m_maxStandardAngle, 'f', 1) << generateRemarks();

        dataLines.append(record.join(","));

        // 发射进度信号
        emit generationProgress(i + 1, recordCount);
    }

    if (writeCSVFile(filePath, QStringList(), dataLines)) {
        m_generatedFiles.append(filePath);
        emit fileGenerated(filePath);
        qDebug() << "✅ 正常CSV文件生成成功:" << filePath;
        return filePath;
    }

    qDebug() << "❌ 正常CSV文件生成失败:" << filePath;
    return QString();
}

QString TestDataGenerator::generateDuplicateCSV(const QString &fileName, int duplicateCount) {
    qDebug() << "生成包含重复数据的CSV文件:" << fileName << "重复数量:" << duplicateCount;

    QString     filePath = m_outputDirectory + fileName;
    QStringList headers = {"时间戳", "电机标签", "MCUID", "固件版本", "测试结果", "最小俯仰角", "最大俯仰角", "俯仰角下限", "俯仰角上限", "备注"};

    QStringList dataLines;
    dataLines.append(headers.join(","));

    // 生成基础数据
    QString baseMotorLabel = generateMotorLabel();
    QString baseMCUID      = generateMCUID();

    // 添加原始数据
    QStringList baseRecord;
    baseRecord << generateTimestamp() << baseMotorLabel << baseMCUID << m_firmwareVersion << QString::number(generateTestResult())
               << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1) << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1)
               << QString::number(m_minStandardAngle, 'f', 1) << QString::number(m_maxStandardAngle, 'f', 1) << "原始测试数据";

    dataLines.append(baseRecord.join(","));

    // 添加一些其他正常数据
    for (int i = 0; i < 3; ++i) {
        QStringList record;
        record << generateTimestamp() << generateMotorLabel() << generateMCUID() << m_firmwareVersion << QString::number(generateTestResult())
               << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1) << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1)
               << QString::number(m_minStandardAngle, 'f', 1) << QString::number(m_maxStandardAngle, 'f', 1) << "正常测试数据";

        dataLines.append(record.join(","));
    }

    // 添加重复数据（相同电机标签，但时间戳和测试结果不同）
    for (int i = 0; i < duplicateCount; ++i) {
        QStringList duplicateRecord;
        duplicateRecord << generateTimestamp() << baseMotorLabel  // 使用相同的电机标签
                        << baseMCUID << m_firmwareVersion << QString::number(generateTestResult())
                        << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1)
                        << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1) << QString::number(m_minStandardAngle, 'f', 1)
                        << QString::number(m_maxStandardAngle, 'f', 1) << QString("重复测试数据-%1").arg(i + 1);

        dataLines.append(duplicateRecord.join(","));
    }

    if (writeCSVFile(filePath, QStringList(), dataLines)) {
        m_generatedFiles.append(filePath);
        emit fileGenerated(filePath);
        qDebug() << "✅ 重复数据CSV文件生成成功:" << filePath;
        return filePath;
    }

    qDebug() << "❌ 重复数据CSV文件生成失败:" << filePath;
    return QString();
}

QString TestDataGenerator::generateInvalidCSV(const QString &fileName, ExceptionType type) {
    qDebug() << "生成异常CSV文件:" << fileName << "异常类型:" << static_cast<int>(type);

    QString     filePath = m_outputDirectory + fileName;
    QStringList dataLines;

    switch (type) {
    case ExceptionType::InvalidFormat:
        // 格式错误：缺少必要的列
        dataLines.append("无效头部");
        dataLines.append("缺少列,数据");
        dataLines.append("12345,MCU001,V1.2.3");  // 列数不足
        break;

    case ExceptionType::MissingColumns:
        // 缺少列：只有部分列
        dataLines.append("电机标签,MCUID,固件版本");
        dataLines.append("12345678,MCU001234,V1.6.15.11.25.2.25");
        break;

    case ExceptionType::InvalidDataTypes:
        // 数据类型错误
        dataLines.append("时间戳,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,备注");
        dataLines.append("invalid,12345678,MCU001,V1.2.3,abc,def,ghi,jkl,mno,错误数据类型");
        break;

    case ExceptionType::EmptyFile:
        // 空文件 - 不添加任何内容
        break;

    case ExceptionType::CorruptedData:
        // 损坏的数据
        dataLines.append("时间戳,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,备注");
        dataLines.append("2025/01/17 10:00:00,12345678,MCU001,V1.2.3,1,-15.5,15.2,-16.0,16.0,正常数据");
        dataLines.append("损坏的数据行，格式完全错误");
        dataLines.append("2025/01/17 10:01:00,,MCU002,,1,abc,def,,ghi,缺少关键字段");
        break;

    default:
        qDebug() << "未知的异常类型，生成默认异常数据";
        dataLines.append("未知异常类型");
        break;
    }

    if (writeCSVFile(filePath, QStringList(), dataLines)) {
        m_generatedFiles.append(filePath);
        emit fileGenerated(filePath);
        qDebug() << "✅ 异常CSV文件生成成功:" << filePath;
        return filePath;
    }

    qDebug() << "❌ 异常CSV文件生成失败:" << filePath;
    return QString();
}

QString TestDataGenerator::generateLargeCSV(const QString &fileName, int recordCount) {
    qDebug() << "生成大数据量CSV文件:" << fileName << "记录数:" << recordCount;

    // 对于大数据量，使用分批写入以提高性能
    QString filePath = m_outputDirectory + fileName;
    QFile   file(filePath);

    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "❌ 无法创建大数据量文件:" << filePath;
        return QString();
    }

    QTextStream out(&file);

    // 写入头部
    QStringList headers = {"时间戳", "电机标签", "MCUID", "固件版本", "测试结果", "最小俯仰角", "最大俯仰角", "俯仰角下限", "俯仰角上限", "备注"};
    out << headers.join(",") << "\n";

    // 分批写入数据
    const int batchSize = 100;
    for (int i = 0; i < recordCount; ++i) {
        QStringList record;
        record << generateTimestamp() << generateMotorLabel() << generateMCUID() << m_firmwareVersion << QString::number(generateTestResult())
               << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1) << QString::number(generateRandomAngle(m_minAngle, m_maxAngle), 'f', 1)
               << QString::number(m_minStandardAngle, 'f', 1) << QString::number(m_maxStandardAngle, 'f', 1) << QString("大数据测试-%1").arg(i + 1);

        out << record.join(",") << "\n";

        // 每批次刷新一次
        if ((i + 1) % batchSize == 0) {
            out.flush();
            emit generationProgress(i + 1, recordCount);
        }
    }

    file.close();

    m_generatedFiles.append(filePath);
    emit fileGenerated(filePath);
    qDebug() << "✅ 大数据量CSV文件生成成功:" << filePath;
    return filePath;
}

// 私有辅助方法实现
QString TestDataGenerator::generateMotorLabel() {
    // 生成8位数字的电机标签
    QString label;
    do {
        label = QString::number(QRandomGenerator::global()->bounded(10000000, 99999999));
    } while (m_usedMotorLabels.contains(label));

    m_usedMotorLabels.append(label);
    return label;
}

QString TestDataGenerator::generateMCUID() {
    // 生成MCU ID格式：MCU + 6位数字
    int number = QRandomGenerator::global()->bounded(100000, 999999);
    return QString("MCU%1").arg(number, 6, 10, QChar('0'));
}

QString TestDataGenerator::generateRandomFirmwareVersion() {
    // 生成随机固件版本
    int major = QRandomGenerator::global()->bounded(1, 3);
    int minor = QRandomGenerator::global()->bounded(0, 10);
    int patch = QRandomGenerator::global()->bounded(0, 20);
    return QString("V%1.%2.%3").arg(major).arg(minor).arg(patch);
}

QString TestDataGenerator::generateTimestamp() {
    // 在设定的时间范围内生成随机时间戳
    qint64 startMsecs   = m_startDateTime.toMSecsSinceEpoch();
    qint64 endMsecs     = m_endDateTime.toMSecsSinceEpoch();
    qint64 range        = endMsecs - startMsecs;
    qint64 randomOffset = static_cast<qint64>(QRandomGenerator::global()->generateDouble() * range);
    qint64 randomMsecs  = startMsecs + randomOffset;

    QDateTime randomDateTime = QDateTime::fromMSecsSinceEpoch(randomMsecs);
    return randomDateTime.toString("yyyy/MM/dd hh:mm:ss");
}

float TestDataGenerator::generateRandomAngle(float min, float max) {
    // 生成指定范围内的随机角度值
    float range       = max - min;
    float randomValue = QRandomGenerator::global()->generateDouble() * range + min;
    return qRound(randomValue * 10.0f) / 10.0f;  // 保留一位小数
}

int TestDataGenerator::generateTestResult() {
    // 90%概率生成成功结果(1)，10%概率生成失败结果(0)
    return QRandomGenerator::global()->bounded(100) < 90 ? 1 : 0;
}

QString TestDataGenerator::generateRemarks() {
    QStringList remarks = {"自动化测试数据", "模拟测试结果", "性能测试数据", "功能验证数据", "集成测试数据", "回归测试数据"};

    int index = QRandomGenerator::global()->bounded(remarks.size());
    return remarks[index];
}

bool TestDataGenerator::writeCSVFile(const QString &filePath, const QStringList &headers, const QStringList &data) {
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "❌ 无法创建文件:" << filePath;
        return false;
    }

    QTextStream out(&file);

    // 写入头部（如果提供）
    if (!headers.isEmpty()) {
        out << headers.join(",") << "\n";
    }

    // 写入数据
    for (const QString &line : data) {
        out << line << "\n";
    }

    return true;
}

// 配置方法实现
void TestDataGenerator::setOutputDirectory(const QString &dir) {
    m_outputDirectory = dir;
    if (!m_outputDirectory.endsWith("/")) {
        m_outputDirectory += "/";
    }
    QDir().mkpath(m_outputDirectory);
    qDebug() << "输出目录设置为:" << m_outputDirectory;
}

QString TestDataGenerator::getOutputDirectory() const {
    return m_outputDirectory;
}

void TestDataGenerator::clearGeneratedFiles() {
    for (const QString &filePath : m_generatedFiles) {
        if (QFile::exists(filePath)) {
            QFile::remove(filePath);
            qDebug() << "删除生成的文件:" << filePath;
        }
    }
    m_generatedFiles.clear();
    m_usedMotorLabels.clear();
}

QStringList TestDataGenerator::getGeneratedFiles() const {
    return m_generatedFiles;
}

void TestDataGenerator::setFirmwareVersion(const QString &version) {
    m_firmwareVersion = version;
    qDebug() << "固件版本设置为:" << m_firmwareVersion;
}

void TestDataGenerator::setDateTimeRange(const QDateTime &start, const QDateTime &end) {
    m_startDateTime = start;
    m_endDateTime   = end;
    qDebug() << "时间范围设置为:" << start.toString() << "到" << end.toString();
}

void TestDataGenerator::setAngleRange(float minAngle, float maxAngle) {
    m_minAngle = minAngle;
    m_maxAngle = maxAngle;
    qDebug() << "角度范围设置为:" << minAngle << "到" << maxAngle;
}

void TestDataGenerator::setStandardAngleRange(float minStandard, float maxStandard) {
    m_minStandardAngle = minStandard;
    m_maxStandardAngle = maxStandard;
    qDebug() << "标准角度范围设置为:" << minStandard << "到" << maxStandard;
}
