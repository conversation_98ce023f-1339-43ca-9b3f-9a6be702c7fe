The system is: Windows - 10.0.19045 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.0+0538acc04
版权所有(C) Microsoft Corporation。保留所有权利。

生成启动时间为 2025/7/17 22:23:47。
节点 1 上的项目“F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\3.21.2\CompilerIdC\CompilerIdC.vcxproj”(默认目标)。
PrepareForBuild:
  正在创建目录“Debug\”。
  正在创建目录“Debug\CompilerIdC.tlog\”。
InitializeBuildStatus:
  正在创建“Debug\CompilerIdC.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
ClCompile:
  D:\Programs\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
  CMakeCCompilerId.c
Link:
  D:\Programs\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdC.lib" /MACHINE:X64 Debug\CMakeCCompilerId.obj
  CompilerIdC.vcxproj -> F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\3.21.2\CompilerIdC\CompilerIdC.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_C_COMPILER=D:\Programs\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  正在删除文件“Debug\CompilerIdC.tlog\unsuccessfulbuild”。
  正在对“Debug\CompilerIdC.tlog\CompilerIdC.lastbuildstate”执行 Touch 任务。
已完成生成项目“F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\3.21.2\CompilerIdC\CompilerIdC.vcxproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:07.25


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"

The C compiler identification is MSVC, found in "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/CMakeFiles/3.21.2/CompilerIdC/CompilerIdC.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.0+0538acc04
版权所有(C) Microsoft Corporation。保留所有权利。

生成启动时间为 2025/7/17 22:23:57。
节点 1 上的项目“F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\3.21.2\CompilerIdCXX\CompilerIdCXX.vcxproj”(默认目标)。
PrepareForBuild:
  正在创建目录“Debug\”。
  正在创建目录“Debug\CompilerIdCXX.tlog\”。
InitializeBuildStatus:
  正在创建“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
ClCompile:
  D:\Programs\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
  CMakeCXXCompilerId.cpp
Link:
  D:\Programs\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdCXX.lib" /MACHINE:X64 Debug\CMakeCXXCompilerId.obj
  CompilerIdCXX.vcxproj -> F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\3.21.2\CompilerIdCXX\CompilerIdCXX.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_CXX_COMPILER=D:\Programs\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  正在删除文件“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”。
  正在对“Debug\CompilerIdCXX.tlog\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
已完成生成项目“F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\3.21.2\CompilerIdCXX\CompilerIdCXX.vcxproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:09.11


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"

The CXX compiler identification is MSVC, found in "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/CMakeFiles/3.21.2/CompilerIdCXX/CompilerIdCXX.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Programs/Microsoft Visual Studio/2019/Professional/MSBuild/Current/Bin/MSBuild.exe cmTC_dec46.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:m && 用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.0+0538acc04

版权所有(C) Microsoft Corporation。保留所有权利。



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30133 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_dec46.dir\Debug\\" /Fd"cmTC_dec46.dir\Debug\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCCompilerABI.c"

  CMakeCCompilerABI.c

  cmTC_dec46.vcxproj -> F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\CMakeTmp\Debug\cmTC_dec46.exe




Detecting CXX compiler ABI info compiled with the following output:
Change Dir: F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Programs/Microsoft Visual Studio/2019/Professional/MSBuild/Current/Bin/MSBuild.exe cmTC_055b0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:m && 用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.0+0538acc04

版权所有(C) Microsoft Corporation。保留所有权利。



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30133 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_055b0.dir\Debug\\" /Fd"cmTC_055b0.dir\Debug\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXCompilerABI.cpp"

  CMakeCXXCompilerABI.cpp

  cmTC_055b0.vcxproj -> F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\tests\build\CMakeFiles\CMakeTmp\Debug\cmTC_055b0.exe




