#include <QApplication>
#include <QDebug>
#include <QtTest/QtTest>
#include <QFileInfo>
#include <QDir>
#include <QSignalSpy>
#include "../widget.h"
#include "../CSV/CSVReader.h"

/**
 * @brief 真实CSV文件测试类
 * 
 * 专门测试真实CSV文件的处理，包括格式转换和MES上传模拟
 */
class RealCSVTest : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 核心测试用例
    void testRealCSVFormatAnalysis();
    void testCSVFormatConversion();
    void testMESUploadWithRealData();
    void testSignalEmissionWithRealData();

private:
    Widget* m_widget;
    CSVReader* m_csvReader;
    QString m_realDataFile;
    QString m_testDataDir;
    
    // 辅助方法
    QString convertRealCSVToExpectedFormat(const QString& realFile);
    void analyzeCSVFormat(const QString& filePath);
};

void RealCSVTest::initTestCase()
{
    qDebug() << "=== 真实CSV文件测试开始 ===";
    
    m_testDataDir = QCoreApplication::applicationDirPath() + "/test_data/";
    QDir().mkpath(m_testDataDir);
    
    m_realDataFile = m_testDataDir + "real_data.csv";
    
    qDebug() << "测试数据目录:" << m_testDataDir;
    qDebug() << "真实数据文件:" << m_realDataFile;
}

void RealCSVTest::cleanupTestCase()
{
    qDebug() << "=== 真实CSV文件测试结束 ===";
}

void RealCSVTest::init()
{
    m_widget = new Widget();
    m_csvReader = new CSVReader();
}

void RealCSVTest::cleanup()
{
    delete m_widget;
    delete m_csvReader;
    m_widget = nullptr;
    m_csvReader = nullptr;
}

void RealCSVTest::testRealCSVFormatAnalysis()
{
    qDebug() << "\n--- 测试真实CSV格式分析 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    analyzeCSVFormat(m_realDataFile);
    
    qDebug() << "✅ 真实CSV格式分析完成";
}

void RealCSVTest::testCSVFormatConversion()
{
    qDebug() << "\n--- 测试CSV格式转换 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 转换格式
    QString convertedFile = convertRealCSVToExpectedFormat(m_realDataFile);
    QVERIFY(!convertedFile.isEmpty());
    QVERIFY(QFile::exists(convertedFile));
    
    qDebug() << "转换后的文件:" << convertedFile;
    
    // 测试转换后的文件是否能被CSVReader正确解析
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(convertedFile);
    qDebug() << "转换后文件解析结果:" << pitchData.size() << "条数据";
    
    QVERIFY(pitchData.size() > 0);
    
    if (pitchData.size() > 0) {
        const PitchData& firstData = pitchData[0];
        qDebug() << "转换后第一条数据:";
        qDebug() << "- 电机标签:" << firstData.getNbr();
        qDebug() << "- MCU ID:" << firstData.getMcuID();
        qDebug() << "- 固件版本:" << firstData.getFirmwareVersion();
        qDebug() << "- 测试结果:" << firstData.getTestResult();
        
        QVERIFY(!firstData.getNbr().isEmpty());
        QVERIFY(!firstData.getMcuID().isEmpty());
        QCOMPARE(firstData.getFirmwareVersion(), QString("V1.**********.2.25"));
    }
    
    qDebug() << "✅ CSV格式转换测试通过";
}

void RealCSVTest::testMESUploadWithRealData()
{
    qDebug() << "\n--- 测试使用真实数据的MES上传 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 转换格式
    QString convertedFile = convertRealCSVToExpectedFormat(m_realDataFile);
    QVERIFY(!convertedFile.isEmpty());
    
    // 读取数据
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(convertedFile);
    qDebug() << "读取到真实数据:" << pitchData.size() << "条";
    QVERIFY(pitchData.size() > 0);
    
    // 创建工作拷贝
    QString workingCopy = m_csvReader->createWorkingCopy(convertedFile);
    QVERIFY(!workingCopy.isEmpty());
    QVERIFY(QFile::exists(workingCopy));
    qDebug() << "工作拷贝创建成功:" << workingCopy;
    
    // 测试去重功能
    QVector<PitchData> uniqueData = m_csvReader->removeDuplicatesKeepLatest(pitchData);
    qDebug() << "去重前:" << pitchData.size() << "条，去重后:" << uniqueData.size() << "条";
    
    // 测试状态更新
    if (uniqueData.size() > 0) {
        QString firstNbr = uniqueData[0].getNbr();
        bool updateResult = m_csvReader->updateUploadStatusByNbr(workingCopy, firstNbr, UploadStatus::Uploaded);
        QVERIFY(updateResult);
        qDebug() << "状态更新成功 - 电机标签:" << firstNbr;
    }
    
    qDebug() << "✅ 真实数据MES上传测试通过";
}

void RealCSVTest::testSignalEmissionWithRealData()
{
    qDebug() << "\n--- 测试真实数据的信号发射 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 设置信号监听器
    QSignalSpy workingCopyCreatedSpy(m_csvReader, &CSVReader::workingCopyCreated);
    QSignalSpy statusUpdatedSpy(m_csvReader, &CSVReader::statusUpdated);
    
    QVERIFY(workingCopyCreatedSpy.isValid());
    QVERIFY(statusUpdatedSpy.isValid());
    
    // 转换格式并处理
    QString convertedFile = convertRealCSVToExpectedFormat(m_realDataFile);
    QVERIFY(!convertedFile.isEmpty());
    
    // 创建工作拷贝（应该触发信号）
    QString workingCopy = m_csvReader->createWorkingCopy(convertedFile);
    QVERIFY(!workingCopy.isEmpty());
    
    // 验证工作拷贝创建信号
    QCOMPARE(workingCopyCreatedSpy.count(), 1);
    qDebug() << "工作拷贝创建信号发射次数:" << workingCopyCreatedSpy.count();
    
    // 读取数据并更新状态（应该触发状态更新信号）
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(convertedFile);
    if (pitchData.size() > 0) {
        QString firstNbr = pitchData[0].getNbr();
        m_csvReader->updateUploadStatusByNbr(workingCopy, firstNbr, UploadStatus::Uploaded);
        
        // 验证状态更新信号
        QCOMPARE(statusUpdatedSpy.count(), 1);
        qDebug() << "状态更新信号发射次数:" << statusUpdatedSpy.count();
    }
    
    qDebug() << "✅ 真实数据信号发射测试通过";
}

void RealCSVTest::analyzeCSVFormat(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件:" << filePath;
        return;
    }
    
    QTextStream in(&file);
    QString firstLine = in.readLine();
    QString secondLine = in.readLine();
    
    qDebug() << "=== CSV格式分析 ===";
    qDebug() << "文件路径:" << filePath;
    qDebug() << "第一行（头部）:" << firstLine;
    qDebug() << "第二行（数据）:" << secondLine;
    
    QStringList headers = firstLine.split(",");
    QStringList dataFields = secondLine.split(",");
    
    qDebug() << "头部列数:" << headers.size();
    qDebug() << "数据列数:" << dataFields.size();
    
    for (int i = 0; i < headers.size() && i < dataFields.size(); ++i) {
        qDebug() << QString("第%1列: %2 = %3").arg(i+1).arg(headers[i]).arg(dataFields[i]);
    }
    
    file.close();
}

QString RealCSVTest::convertRealCSVToExpectedFormat(const QString& realFile)
{
    QFile inputFile(realFile);
    if (!inputFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开输入文件:" << realFile;
        return QString();
    }
    
    QString outputFile = m_testDataDir + "converted_real_data.csv";
    QFile outFile(outputFile);
    if (!outFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法创建输出文件:" << outputFile;
        return QString();
    }
    
    QTextStream in(&inputFile);
    QTextStream out(&outFile);
    out.setCodec("UTF-8");
    
    // 跳过头部行
    if (!in.atEnd()) {
        QString headerLine = in.readLine();
        qDebug() << "跳过头部行:" << headerLine;
    }
    
    // 转换数据行
    int lineCount = 0;
    while (!in.atEnd()) {
        QString line = in.readLine();
        QStringList fields = line.split(",");
        
        if (fields.size() >= 10) {
            // 真实格式：时间,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,测试数据
            // 期望格式：电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限
            QString convertedLine = QString("%1,%2,%3,%4,%5,%6,%7,%8")
                .arg(fields[1])  // 电机标签
                .arg(fields[2])  // MCUID
                .arg(fields[3])  // 固件版本
                .arg(fields[4])  // 测试结果
                .arg(fields[5])  // 最小俯仰角
                .arg(fields[6])  // 最大俯仰角
                .arg(fields[7])  // 俯仰角下限
                .arg(fields[8]); // 俯仰角上限
            
            out << convertedLine << "\n";
            lineCount++;
        }
    }
    
    inputFile.close();
    outFile.close();
    
    qDebug() << "格式转换完成，转换了" << lineCount << "行数据";
    qDebug() << "输出文件:" << outputFile;
    
    return outputFile;
}

QTEST_MAIN(RealCSVTest)
#include "RealCSVTest.moc"
